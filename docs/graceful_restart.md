# FastAPI 生产环境平滑重启指南

## 概述

本文档介绍了基于 **Gunicorn + Uvicorn Workers** 的FastAPI应用平滑重启方案，实现零停机部署。

## 核心方案：Gunicorn + 信号处理

### 特点
- 使用Gunicorn作为WSGI服务器，Uvicorn作为Worker
- 支持优雅重启（SIGHUP信号）
- 零停机时间
- 配置简单

### 使用方法

1. **启动服务**
```bash
# 使用Docker
docker run -d --name ai_server -p 30101:30101 \
  -v /data/server/logs:/app/logs \
  -e ENVIRONMENT=production \
  ************:9092/ai_server:latest

# 或直接使用Poetry
poetry run gunicorn app.main:app -c gunicorn.conf.py
```

2. **平滑重启**
```bash
# 方法1：使用脚本（推荐）
./scripts/graceful_restart.sh

# 方法2：发送信号
docker kill --signal=SIGHUP ai_server

# 方法3：使用Gunicorn命令
kill -HUP $(pgrep -f gunicorn)
```

### 配置说明

Gunicorn配置文件 `gunicorn.conf.py` 的关键参数：

```python
# Worker进程数（建议：CPU核心数 * 2 + 1）
workers = multiprocessing.cpu_count() * 2 + 1

# Worker类型
worker_class = "uvicorn.workers.UvicornWorker"

# 优雅关闭超时时间
graceful_timeout = 30

# 预加载应用（提高重启速度）
preload_app = True
```

## 工作原理

### Gunicorn优雅重启机制
1. **SIGHUP信号**: 向Gunicorn主进程发送SIGHUP信号
2. **Worker重载**: 主进程逐个重启Worker进程
3. **零停机**: 旧Worker处理完当前请求后才退出
4. **新Worker**: 使用新代码启动新的Worker进程

### 应用层支持
- **lifespan事件**: 处理应用启动和关闭
- **健康检查**: 提供服务状态监控
- **日志记录**: 完整的启动关闭日志

## 监控和运维

### 使用监控脚本

```bash
# 检查当前状态
./scripts/monitor.sh --status

# 开始监控
./scripts/monitor.sh --monitor

# 查看监控日志
./scripts/monitor.sh --logs
```

### 关键指标监控

1. **健康检查响应时间**
2. **活跃请求数量**
3. **容器资源使用率**
4. **重启成功率**

## 最佳实践

### 1. 部署前检查
- 确保新版本通过所有测试
- 验证配置文件正确性
- 检查依赖服务可用性

### 2. 部署过程
- 使用自动化脚本
- 监控关键指标
- 准备回滚方案

### 3. 部署后验证
- 执行健康检查
- 验证核心功能
- 监控错误日志

### 4. 回滚策略
```bash
# 快速回滚到上一个版本
docker tag ai_server:current ai_server:backup
docker tag ai_server:previous ai_server:current
./scripts/graceful_restart.sh
```

## 故障排除

### 常见问题

1. **重启超时**
   - 检查 `graceful_timeout` 设置
   - 查看是否有长时间运行的请求
   - 增加超时时间或优化请求处理

2. **健康检查失败**
   - 检查应用启动时间
   - 验证健康检查端点
   - 查看应用日志

3. **内存泄漏**
   - 监控内存使用趋势
   - 定期重启Worker进程
   - 优化代码中的内存使用

### 日志分析

重要日志关键词：
- `服务正在启动`: 应用启动
- `收到信号`: 开始优雅关闭
- `等待活跃请求完成`: 等待请求完成
- `服务已关闭`: 关闭完成

## 性能调优

### Gunicorn参数优化

```python
# 根据实际情况调整
workers = 4  # CPU密集型应用可以设置为CPU核心数
worker_connections = 1000  # 每个Worker的连接数
max_requests = 1000  # Worker处理请求数上限
max_requests_jitter = 50  # 随机重启避免同时重启
```

### 系统级优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化TCP参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
```

## 总结

**Gunicorn + Uvicorn Workers** 方案的优势：

1. **✅ 零停机时间** - 真正的平滑重启
2. **✅ 配置简单** - 只需要Gunicorn配置文件
3. **✅ 资源友好** - 不需要额外的资源开销
4. **✅ 久经考验** - 生产环境广泛使用
5. **✅ 自动回滚** - 新版本启动失败时自动回滚

这个方案适合大多数FastAPI生产环境部署场景，提供了可靠的零停机部署能力。
