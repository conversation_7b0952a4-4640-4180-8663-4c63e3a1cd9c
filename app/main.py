import asyncio
import logging
import signal
import time
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI

from app.api.v1.routers import api_router
from app.core.config import settings
from app.core.log import setup_logging
from app.middleware.graceful_shutdown import GracefulShutdownMiddleware
from app.middleware.log import LoggingMiddleware
from app.middleware.request_id import RequestIdMiddleware
from app.middleware.response_headers import ResponseHeadersMiddleware
from app.schemas.common import HealthCheckResponse


# 全局变量用于优雅关闭
shutdown_event = asyncio.Event()
active_requests = set()


class GracefulShutdownHandler:
    """优雅关闭处理器"""

    def __init__(self):
        self.shutdown_initiated = False

    def signal_handler(self, signum, frame):
        """信号处理函数"""
        if not self.shutdown_initiated:
            self.shutdown_initiated = True
            logging.info(f"收到信号 {signum}，开始优雅关闭...")
            shutdown_event.set()

    async def wait_for_requests_completion(self, timeout: int = 30):
        """等待活跃请求完成"""
        if not active_requests:
            return

        logging.info(f"等待 {len(active_requests)} 个活跃请求完成...")
        start_time = time.time()

        while active_requests and (time.time() - start_time) < timeout:
            await asyncio.sleep(0.1)

        if active_requests:
            logging.warning(f"超时后仍有 {len(active_requests)} 个请求未完成")
        else:
            logging.info("所有活跃请求已完成")


shutdown_handler = GracefulShutdownHandler()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时设置信号处理
    setup_logging()

    # 注册信号处理器
    signal.signal(signal.SIGTERM, shutdown_handler.signal_handler)
    signal.signal(signal.SIGINT, shutdown_handler.signal_handler)

    logging.info(
        f"服务正在启动... 环境: {settings.ENVIRONMENT}, 版本: {settings.VERSION}"
    )

    yield

    # 关闭时的清理工作
    logging.info("开始优雅关闭...")

    # 等待活跃请求完成
    await shutdown_handler.wait_for_requests_completion()

    logging.info("服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="AI Server",
        description="基于FastAPI的AI服务后端项目",
        version=settings.VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan,
    )

    # 添加优雅关闭中间件（最先添加，最后执行）
    app.add_middleware(
        GracefulShutdownMiddleware,
        active_requests=active_requests,
        shutdown_event=shutdown_event
    )
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIdMiddleware)
    app.add_middleware(ResponseHeadersMiddleware)
    #
    app.include_router(api_router)

    return app


app = create_app()


@app.get("/", include_in_schema=False)
def read_root():
    return {"message": "AI Server API", "docs": "/docs", "health": "/health"}


@app.get("/health", response_model=HealthCheckResponse, tags=["系统"])
async def health_check():
    # 检查是否正在关闭
    if shutdown_event.is_set():
        return HealthCheckResponse(
            status="shutting_down",
            timestamp=time.time(),
            version=settings.VERSION,
            environment=settings.ENVIRONMENT,
        )

    return HealthCheckResponse(
        status="healthy",
        timestamp=time.time(),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
