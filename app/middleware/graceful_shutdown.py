"""优雅关闭中间件"""

import asyncio
import logging
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class GracefulShutdownMiddleware(BaseHTTPMiddleware):
    """优雅关闭中间件 - 跟踪活跃请求"""
    
    def __init__(self, app, active_requests: set, shutdown_event: asyncio.Event):
        super().__init__(app)
        self.active_requests = active_requests
        self.shutdown_event = shutdown_event
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查是否正在关闭
        if self.shutdown_event.is_set():
            logger.warning("服务正在关闭，拒绝新请求")
            return Response(
                content="Service is shutting down",
                status_code=503,
                headers={"Retry-After": "10"}
            )
        
        # 生成请求ID并添加到活跃请求集合
        request_id = str(uuid.uuid4())
        self.active_requests.add(request_id)
        
        try:
            # 处理请求
            response = await call_next(request)
            return response
        
        except Exception as e:
            logger.error(f"请求处理异常: {e}")
            raise
        
        finally:
            # 从活跃请求集合中移除
            self.active_requests.discard(request_id)
