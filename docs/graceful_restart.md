# FastAPI 生产环境平滑重启指南

## 概述

本文档介绍了FastAPI应用在生产环境中实现平滑重启（零停机部署）的几种方案，确保在更新部署时不影响用户使用。

## 方案对比

| 方案 | 复杂度 | 停机时间 | 资源消耗 | 推荐场景 |
|------|--------|----------|----------|----------|
| Gunicorn + 信号处理 | 低 | 0秒 | 低 | 单机部署 |
| Docker Compose + 滚动更新 | 中 | 0秒 | 中 | 多实例部署 |
| 蓝绿部署 | 高 | 0秒 | 高 | 大规模生产环境 |

## 方案一：Gunicorn + 信号处理（推荐）

### 特点
- 使用Gunicorn作为WSGI服务器，Uvicorn作为Worker
- 支持优雅重启（SIGHUP信号）
- 零停机时间
- 配置简单

### 使用方法

1. **启动服务**
```bash
# 使用Docker
docker run -d --name ai_server -p 30101:30101 \
  -v /data/server/logs:/app/logs \
  -e ENVIRONMENT=production \
  ************:9092/ai_server:latest

# 或直接使用Poetry
poetry run gunicorn app.main:app -c gunicorn.conf.py
```

2. **平滑重启**
```bash
# 方法1：使用脚本（推荐）
./scripts/graceful_restart.sh

# 方法2：发送信号
docker kill --signal=SIGHUP ai_server

# 方法3：使用Gunicorn命令
kill -HUP $(pgrep -f gunicorn)
```

### 配置说明

Gunicorn配置文件 `gunicorn.conf.py` 的关键参数：

```python
# Worker进程数（建议：CPU核心数 * 2 + 1）
workers = multiprocessing.cpu_count() * 2 + 1

# Worker类型
worker_class = "uvicorn.workers.UvicornWorker"

# 优雅关闭超时时间
graceful_timeout = 30

# 预加载应用（提高重启速度）
preload_app = True
```

## 方案二：Docker Compose + 滚动更新

### 特点
- 使用Docker Compose管理多个服务实例
- 支持滚动更新
- 内置负载均衡
- 适合多实例部署

### 使用方法

1. **启动服务**
```bash
docker-compose up -d
```

2. **滚动更新**
```bash
# 拉取新镜像
docker-compose pull

# 滚动更新
docker-compose up -d --no-deps --scale ai_server=2 ai_server
```

## 方案三：蓝绿部署

### 特点
- 维护两套完全相同的生产环境
- 瞬间切换
- 可快速回滚
- 资源消耗大

### 实现步骤

1. **准备蓝绿环境**
```bash
# 蓝环境（当前生产）
docker run -d --name ai_server_blue -p 30101:30101 ...

# 绿环境（新版本）
docker run -d --name ai_server_green -p 30102:30101 ...
```

2. **切换流量**
```bash
# 使用Nginx或负载均衡器切换上游服务器
# 从 ai_server_blue:30101 切换到 ai_server_green:30101
```

## 应用层优化

### 信号处理

应用已集成优雅关闭处理：

```python
# 注册信号处理器
signal.signal(signal.SIGTERM, shutdown_handler.signal_handler)
signal.signal(signal.SIGINT, shutdown_handler.signal_handler)

# 等待活跃请求完成
await shutdown_handler.wait_for_requests_completion()
```

### 中间件支持

`GracefulShutdownMiddleware` 提供：
- 活跃请求跟踪
- 关闭期间拒绝新请求
- 优雅的503响应

### 健康检查增强

健康检查端点 `/health` 支持状态报告：
- `healthy`: 服务正常
- `shutting_down`: 正在关闭
- 其他状态表示异常

## 监控和运维

### 使用监控脚本

```bash
# 检查当前状态
./scripts/monitor.sh --status

# 开始监控
./scripts/monitor.sh --monitor

# 查看监控日志
./scripts/monitor.sh --logs
```

### 关键指标监控

1. **健康检查响应时间**
2. **活跃请求数量**
3. **容器资源使用率**
4. **重启成功率**

## 最佳实践

### 1. 部署前检查
- 确保新版本通过所有测试
- 验证配置文件正确性
- 检查依赖服务可用性

### 2. 部署过程
- 使用自动化脚本
- 监控关键指标
- 准备回滚方案

### 3. 部署后验证
- 执行健康检查
- 验证核心功能
- 监控错误日志

### 4. 回滚策略
```bash
# 快速回滚到上一个版本
docker tag ai_server:current ai_server:backup
docker tag ai_server:previous ai_server:current
./scripts/graceful_restart.sh
```

## 故障排除

### 常见问题

1. **重启超时**
   - 检查 `graceful_timeout` 设置
   - 查看是否有长时间运行的请求
   - 增加超时时间或优化请求处理

2. **健康检查失败**
   - 检查应用启动时间
   - 验证健康检查端点
   - 查看应用日志

3. **内存泄漏**
   - 监控内存使用趋势
   - 定期重启Worker进程
   - 优化代码中的内存使用

### 日志分析

重要日志关键词：
- `服务正在启动`: 应用启动
- `收到信号`: 开始优雅关闭
- `等待活跃请求完成`: 等待请求完成
- `服务已关闭`: 关闭完成

## 性能调优

### Gunicorn参数优化

```python
# 根据实际情况调整
workers = 4  # CPU密集型应用可以设置为CPU核心数
worker_connections = 1000  # 每个Worker的连接数
max_requests = 1000  # Worker处理请求数上限
max_requests_jitter = 50  # 随机重启避免同时重启
```

### 系统级优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化TCP参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' >> /etc/sysctl.conf
```

## 总结

推荐使用 **方案一（Gunicorn + 信号处理）** 作为主要的平滑重启方案，因为它：

1. **简单可靠**：配置简单，久经考验
2. **零停机**：真正的零停机部署
3. **资源友好**：不需要额外的资源开销
4. **易于监控**：集成完善的监控和日志

对于更复杂的场景，可以考虑结合使用多种方案，例如：
- 开发/测试环境：方案一
- 生产环境：方案一 + 负载均衡
- 大规模部署：方案二或方案三
