#!/bin/bash

# FastAPI应用平滑重启脚本
# 使用方法: ./graceful_restart.sh [container_name] [image_name]

set -e

CONTAINER_NAME=${1:-"ai_server"}
IMAGE_NAME=${2:-"************:9092/ai_server:latest"}
HEALTH_CHECK_URL="http://localhost:30101/health"
MAX_WAIT_TIME=60

echo "开始平滑重启 $CONTAINER_NAME..."

# 检查容器是否存在
if ! docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "容器 $CONTAINER_NAME 不存在，直接启动新容器"
    docker run -d --name $CONTAINER_NAME -p 30101:30101 \
        -v /data/server/logs:/app/logs \
        -e ENVIRONMENT=production \
        -e DEBUG=false \
        -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
        -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
        -e DIFY_API_URL="$DIFY_API_URL" \
        -e DIFY_USER="$DIFY_USER" \
        --restart unless-stopped \
        $IMAGE_NAME
    exit 0
fi

# 获取当前容器的运行参数
OLD_CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)

if [ -z "$OLD_CONTAINER_ID" ]; then
    echo "容器 $CONTAINER_NAME 未运行，直接启动"
    docker start $CONTAINER_NAME || {
        echo "启动失败，删除旧容器并创建新容器"
        docker rm $CONTAINER_NAME
        docker run -d --name $CONTAINER_NAME -p 30101:30101 \
            -v /data/server/logs:/app/logs \
            -e ENVIRONMENT=production \
            -e DEBUG=false \
            -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
            -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
            -e DIFY_API_URL="$DIFY_API_URL" \
            -e DIFY_USER="$DIFY_USER" \
            --restart unless-stopped \
            $IMAGE_NAME
    }
    exit 0
fi

echo "当前容器ID: $OLD_CONTAINER_ID"

# 启动新容器（使用不同端口）
NEW_CONTAINER_NAME="${CONTAINER_NAME}_new"
NEW_PORT=30102

echo "启动新容器 $NEW_CONTAINER_NAME 在端口 $NEW_PORT..."

# 清理可能存在的同名容器
docker rm -f $NEW_CONTAINER_NAME 2>/dev/null || true

# 启动新容器
docker run -d --name $NEW_CONTAINER_NAME -p $NEW_PORT:30101 \
    -v /data/server/logs:/app/logs \
    -e ENVIRONMENT=production \
    -e DEBUG=false \
    -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
    -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
    -e DIFY_API_URL="$DIFY_API_URL" \
    -e DIFY_USER="$DIFY_USER" \
    --restart unless-stopped \
    $IMAGE_NAME

NEW_CONTAINER_ID=$(docker ps -q -f name=$NEW_CONTAINER_NAME)

if [ -z "$NEW_CONTAINER_ID" ]; then
    echo "新容器启动失败"
    exit 1
fi

echo "新容器启动成功，ID: $NEW_CONTAINER_ID"

# 等待新容器健康检查通过
echo "等待新容器健康检查..."
WAIT_TIME=0
while [ $WAIT_TIME -lt $MAX_WAIT_TIME ]; do
    if curl -f -s "http://localhost:$NEW_PORT/health" > /dev/null; then
        echo "新容器健康检查通过"
        break
    fi
    echo "等待中... ($WAIT_TIME/$MAX_WAIT_TIME 秒)"
    sleep 2
    WAIT_TIME=$((WAIT_TIME + 2))
done

if [ $WAIT_TIME -ge $MAX_WAIT_TIME ]; then
    echo "新容器健康检查超时，回滚"
    docker stop $NEW_CONTAINER_NAME
    docker rm $NEW_CONTAINER_NAME
    exit 1
fi

# 向旧容器发送SIGTERM信号进行优雅关闭
echo "向旧容器发送优雅关闭信号..."
docker kill --signal=SIGTERM $OLD_CONTAINER_ID

# 等待旧容器优雅关闭
echo "等待旧容器优雅关闭..."
sleep 10

# 停止旧容器并切换端口
echo "停止旧容器..."
docker stop $CONTAINER_NAME
docker rm $CONTAINER_NAME

# 重新创建主容器使用原端口
echo "重新创建主容器..."
docker run -d --name $CONTAINER_NAME -p 30101:30101 \
    -v /data/server/logs:/app/logs \
    -e ENVIRONMENT=production \
    -e DEBUG=false \
    -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
    -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
    -e DIFY_API_URL="$DIFY_API_URL" \
    -e DIFY_USER="$DIFY_USER" \
    --restart unless-stopped \
    $IMAGE_NAME

# 清理临时容器
echo "清理临时容器..."
docker stop $NEW_CONTAINER_NAME
docker rm $NEW_CONTAINER_NAME

# 最终健康检查
echo "最终健康检查..."
sleep 5
if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
    echo "✅ 平滑重启完成！服务正常运行"
else
    echo "❌ 重启后健康检查失败"
    exit 1
fi
