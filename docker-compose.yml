version: '3.8'

services:
  ai_server:
    image: ************:9092/ai_server:latest
    container_name: ai_server
    ports:
      - "30101:30101"
    volumes:
      - /data/server/logs:/app/logs
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
      - DIFY_SOP_TYPE1_API_KEY=${DIFY_SOP_TYPE1_API_KEY}
      - DIFY_SOP_TYPE2_API_KEY=${DIFY_SOP_TYPE2_API_KEY}
      - DIFY_API_URL=${DIFY_API_URL}
      - DIFY_USER=${DIFY_USER}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:30101/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s

  nginx:
    image: nginx:alpine
    container_name: ai_server_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - ai_server
    restart: unless-stopped
