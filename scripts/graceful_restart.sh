#!/bin/bash

# FastAPI应用平滑重启脚本 - 基于Gunicorn信号处理
# 使用方法: ./graceful_restart.sh [container_name] [image_name]

set -e

CONTAINER_NAME=${1:-"ai_server"}
IMAGE_NAME=${2:-"************:9092/ai_server:latest"}
HEALTH_CHECK_URL="http://localhost:30101/health"
MAX_WAIT_TIME=60

echo "🚀 开始平滑重启 $CONTAINER_NAME (使用Gunicorn优雅重启)..."

# 检查容器是否存在并运行
if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "📦 容器 $CONTAINER_NAME 不存在或未运行，直接启动新容器"
    docker run -d --name $CONTAINER_NAME -p 30101:30101 \
        -v /data/server/logs:/app/logs \
        -e ENVIRONMENT=production \
        -e DEBUG=false \
        -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
        -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
        -e DIFY_API_URL="$DIFY_API_URL" \
        -e DIFY_USER="$DIFY_USER" \
        --restart unless-stopped \
        $IMAGE_NAME

    echo "⏳ 等待新容器启动..."
    sleep 10

    if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
        echo "✅ 新容器启动成功！"
    else
        echo "❌ 新容器启动失败"
        exit 1
    fi
    exit 0
fi

CONTAINER_ID=$(docker ps -q -f name=$CONTAINER_NAME)
echo "📋 当前容器ID: $CONTAINER_ID"

# 检查当前容器健康状态
echo "🔍 检查当前容器状态..."
if ! curl -f -s $HEALTH_CHECK_URL > /dev/null; then
    echo "⚠️  当前容器不健康，执行强制重启"
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
    docker run -d --name $CONTAINER_NAME -p 30101:30101 \
        -v /data/server/logs:/app/logs \
        -e ENVIRONMENT=production \
        -e DEBUG=false \
        -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
        -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
        -e DIFY_API_URL="$DIFY_API_URL" \
        -e DIFY_USER="$DIFY_USER" \
        --restart unless-stopped \
        $IMAGE_NAME
    exit 0
fi

# 使用Gunicorn的SIGHUP信号进行优雅重启
echo "🔄 发送SIGHUP信号给Gunicorn主进程进行优雅重启..."

# 获取容器内Gunicorn主进程PID
GUNICORN_PID=$(docker exec $CONTAINER_NAME pgrep -f "gunicorn.*master" | head -1)

if [ -z "$GUNICORN_PID" ]; then
    echo "❌ 未找到Gunicorn主进程，可能使用的是uvicorn直接启动"
    echo "🔄 使用容器重启方式..."

    # 发送SIGTERM信号优雅关闭
    docker kill --signal=SIGTERM $CONTAINER_ID

    # 等待容器优雅关闭
    echo "⏳ 等待容器优雅关闭..."
    docker wait $CONTAINER_NAME

    # 启动新容器
    docker run -d --name $CONTAINER_NAME -p 30101:30101 \
        -v /data/server/logs:/app/logs \
        -e ENVIRONMENT=production \
        -e DEBUG=false \
        -e DIFY_SOP_TYPE1_API_KEY="$DIFY_SOP_TYPE1_API_KEY" \
        -e DIFY_SOP_TYPE2_API_KEY="$DIFY_SOP_TYPE2_API_KEY" \
        -e DIFY_API_URL="$DIFY_API_URL" \
        -e DIFY_USER="$DIFY_USER" \
        --restart unless-stopped \
        $IMAGE_NAME
else
    echo "📍 找到Gunicorn主进程PID: $GUNICORN_PID"

    # 发送SIGHUP信号给Gunicorn主进程
    docker exec $CONTAINER_NAME kill -HUP $GUNICORN_PID

    echo "⏳ 等待Gunicorn重新加载worker进程..."
    sleep 5
fi

# 等待服务恢复
echo "🔍 等待服务恢复..."
WAIT_TIME=0
while [ $WAIT_TIME -lt $MAX_WAIT_TIME ]; do
    if curl -f -s $HEALTH_CHECK_URL > /dev/null; then
        echo "✅ 服务恢复正常"
        break
    fi
    echo "⏳ 等待中... ($WAIT_TIME/$MAX_WAIT_TIME 秒)"
    sleep 2
    WAIT_TIME=$((WAIT_TIME + 2))
done

if [ $WAIT_TIME -ge $MAX_WAIT_TIME ]; then
    echo "❌ 服务恢复超时"
    exit 1
fi

echo "🎉 平滑重启完成！服务正常运行"

# 显示当前状态
echo "📊 当前服务状态:"
curl -s $HEALTH_CHECK_URL | python3 -m json.tool 2>/dev/null || echo "健康检查响应获取失败"
