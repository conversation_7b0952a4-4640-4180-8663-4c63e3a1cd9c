# FastAPI 生产环境平滑重启方案

## 1. 当前部署架构分析

### 1.1 应用结构
- **框架**: FastAPI
- **端口**: 30101
- **部署方式**: Docker 容器化部署
- **启动命令**: `poetry run uvicorn app.main:app --host 0.0.0.0 --port 30101`

### 1.2 当前部署流程
根据 `.gitlab-ci.yml` 文件分析，当前部署流程为：
1. 构建新的 Docker 镜像
2. 推送到镜像仓库
3. SSH 到生产服务器
4. 停止当前容器 (`docker stop ai_server`)
5. 删除旧容器 (`docker rm ai_server`)
6. 拉取新镜像
7. 启动新容器

### 1.3 当前重启方式的问题
当前部署方式存在明显的停机时间，因为：
- 使用 `docker stop` 直接停止容器，未考虑正在处理的请求
- 先停止再启动的方式导致服务中断
- 没有负载均衡机制来实现零停机部署

## 2. 平滑重启技术方案

### 2.1 方案一：使用 Uvicorn 的 Worker 进程管理 (应用内方案)

FastAPI 基于 Uvicorn，Uvicorn 支持通过信号实现平滑重启：

#### 2.1.1 实现原理
- Uvicorn 支持 `SIGUSR1` 和 `SIGUSR2` 信号用于重新加载 workers
- 可以通过 `kill -USR1 <pid>` 或 `kill -USR2 <pid>` 触发平滑重启
- 在重启过程中，现有连接会继续处理直到完成，新连接会被新进程处理

#### 2.1.2 实施步骤
1. 修改 Docker 启动命令，使用多进程模式：
   ```bash
   poetry run uvicorn app.main:app --host 0.0.0.0 --port 30101 --workers 4
   ```
   
2. 获取主进程 PID 并发送信号：
   ```bash
   # 获取主进程 PID
   PID=$(ps aux | grep "uvicorn app.main:app" | grep -v grep | awk '{print $2}')
   # 发送平滑重启信号
   kill -USR1 $PID
   ```

### 2.2 方案二：使用反向代理和多实例 (推荐)

#### 2.2.1 实现原理
通过 Nginx 或其他反向代理配合多个应用实例实现零停机部署：
1. 运行多个应用实例（不同端口）
2. 使用反向代理分发请求
3. 更新时逐个重启实例，反向代理自动将流量导向健康实例

#### 2.2.2 架构图
```mermaid
graph LR
    A[客户端请求] --> B[Nginx反向代理]
    B --> C[应用实例1 :30101]
    B --> D[应用实例2 :30102]
    B --> E[应用实例3 :30103]
```

#### 2.2.3 实施步骤
1. 部署多个应用实例（不同端口）
2. 配置 Nginx 反向代理
3. 更新时使用滚动重启策略

### 2.3 方案三：使用 Docker Compose 和 Blue-Green 部署

#### 2.3.1 实现原理
通过蓝绿部署策略实现零停机更新：
1. 维护两套环境（蓝色和绿色）
2. 流量默认导向当前环境
3. 更新时部署到备用环境，测试通过后切换流量

#### 2.3.2 架构图
```mermaid
graph LR
    A[客户端请求] --> B[负载均衡器]
    B --> C[蓝色环境]
    B --> D[绿色环境]
    C --> E[应用实例1]
    C --> F[应用实例2]
    D --> G[应用实例3]
    D --> H[应用实例4]
```

## 3. 推荐实施方案

### 3.1 短期方案：优化现有部署流程

#### 3.1.1 修改 Dockerfile
在 `Dockerfile` 中添加健康检查和支持信号处理：

```dockerfile
FROM ************:9092/poetry-ci:latest

# 拷贝项目文件
COPY pyproject.toml poetry.lock ./
COPY app ./app

# 安装项目依赖（不含 dev）
RUN poetry install --only main --no-interaction --no-ansi --no-root

# 暴露端口
EXPOSE 30101

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:30101/health || exit 1

# 启动服务（使用多worker模式）
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "30101", "--workers", "4"]
```
FROM ************:9092/poetry-ci:latest

# 拷贝项目文件
COPY pyproject.toml poetry.lock ./
COPY app ./app

# 安装项目依赖（不含 dev）
RUN poetry install --only main --no-interaction --no-ansi --no-root

# 暴露端口
EXPOSE 30101

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:30101/health || exit 1

# 启动服务（使用多worker模式）
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "30101", "--workers", "4"]
```

#### 3.1.2 创建平滑重启脚本
创建 `scripts/restart.sh`：

```bash
#!/bin/bash

# 平滑重启脚本
echo "开始平滑重启..."

# 获取当前运行的容器ID
CONTAINER_ID=$(docker ps -q -f name=ai_server)

if [ -z "$CONTAINER_ID" ]; then
    echo "未找到运行中的ai_server容器"
    exit 1
fi

echo "当前容器ID: $CONTAINER_ID"

# 获取容器内主进程PID
PID=$(docker exec $CONTAINER_ID ps aux | grep "uvicorn app.main:app" | grep -v grep | awk '{print $2}')

if [ -z "$PID" ]; then
    echo "未找到Uvicorn主进程"
    exit 1
fi

echo "主进程PID: $PID"

# 发送USR1信号触发平滑重启
docker exec $CONTAINER_ID kill -USR1 $PID

echo "已发送平滑重启信号，等待重启完成..."
sleep 5

echo "平滑重启完成"
```
#!/bin/bash

# 平滑重启脚本
echo "开始平滑重启..."

# 获取当前运行的容器ID
CONTAINER_ID=$(docker ps -q -f name=ai_server)

if [ -z "$CONTAINER_ID" ]; then
    echo "未找到运行中的ai_server容器"
    exit 1
fi

echo "当前容器ID: $CONTAINER_ID"

# 获取容器内主进程PID
PID=$(docker exec $CONTAINER_ID ps aux | grep "uvicorn app.main:app" | grep -v grep | awk '{print $2}')

if [ -z "$PID" ]; then
    echo "未找到Uvicorn主进程"
    exit 1
fi

echo "主进程PID: $PID"

# 发送USR1信号触发平滑重启
docker exec $CONTAINER_ID kill -USR1 $PID

echo "已发送平滑重启信号，等待重启完成..."
sleep 5

echo "平滑重启完成"
```

#### 3.1.3 更新 CI/CD 流程
修改 `.gitlab-ci.yml` 中的部署部分：

```yaml
deploy:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    # 上传重启脚本
    - scp scripts/restart.sh $SSH_USER@$SSH_HOST:/tmp/restart.sh
    - ssh $SSH_USER@$SSH_HOST "chmod +x /tmp/restart.sh"
    
    # 拉取新镜像
    - ssh $SSH_USER@$SSH_HOST "docker pull ************:9092/ai_server:latest"
    
    # 尝试平滑重启（如果容器存在）
    - ssh $SSH_USER@$SSH_HOST "/tmp/restart.sh || true"
    
    # 如果平滑重启失败，则执行完整重启
    - ssh $SSH_USER@$SSH_HOST "docker stop ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker rm ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker run -d --name ai_server -p 30101:30101 -v /data/server/logs:/app/logs -e ENVIRONMENT=production -e DEBUG=false -e DIFY_SOP_TYPE1_API_KEY='$DIFY_SOP_TYPE1_API_KEY' -e DIFY_SOP_TYPE2_API_KEY='$DIFY_SOP_TYPE2_API_KEY' -e DIFY_API_URL='$DIFY_API_URL' -e DIFY_USER='$DIFY_USER' --restart unless-stopped ************:9092/ai_server:latest"
  environment:
    name: production
  only:
    - main
```
deploy:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    # 上传重启脚本
    - scp scripts/restart.sh $SSH_USER@$SSH_HOST:/tmp/restart.sh
    - ssh $SSH_USER@$SSH_HOST "chmod +x /tmp/restart.sh"
    
    # 拉取新镜像
    - ssh $SSH_USER@$SSH_HOST "docker pull ************:9092/ai_server:latest"
    
    # 尝试平滑重启（如果容器存在）
    - ssh $SSH_USER@$SSH_HOST "/tmp/restart.sh || true"
    
    # 如果平滑重启失败，则执行完整重启
    - ssh $SSH_USER@$SSH_HOST "docker stop ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker rm ai_server || true"
    - ssh $SSH_USER@$SSH_HOST "docker run -d --name ai_server -p 30101:30101 -v /data/server/logs:/app/logs -e ENVIRONMENT=production -e DEBUG=false -e DIFY_SOP_TYPE1_API_KEY='$DIFY_SOP_TYPE1_API_KEY' -e DIFY_SOP_TYPE2_API_KEY='$DIFY_SOP_TYPE2_API_KEY' -e DIFY_API_URL='$DIFY_API_URL' -e DIFY_USER='$DIFY_USER' --restart unless-stopped ************:9092/ai_server:latest"
  environment:
    name: production
  only:
    - main
```

### 3.2 中长期方案：引入反向代理和多实例

#### 3.2.1 部署架构升级
```mermaid
graph LR
    A[客户端请求] --> B[Nginx]
    B --> C[应用实例1 :30101]
    B --> D[应用实例2 :30102]
    B --> E[应用实例3 :30103]
```

#### 3.2.2 Nginx 配置示例
创建 `nginx.conf`：

```nginx
upstream ai_server_backend {
    server localhost:30101 max_fails=3 fail_timeout=30s;
    server localhost:30102 max_fails=3 fail_timeout=30s;
    server localhost:30103 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://ai_server_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        proxy_pass http://ai_server_backend;
    }
}
```
upstream ai_server_backend {
    server localhost:30101 max_fails=3 fail_timeout=30s;
    server localhost:30102 max_fails=3 fail_timeout=30s;
    server localhost:30103 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://ai_server_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        proxy_pass http://ai_server_backend;
    }
}
```

#### 3.2.3 Docker Compose 配置
创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app1
      - app2
      - app3
    restart: unless-stopped

  app1:
    image: ************:9092/ai_server:latest
    ports:
      - "30101:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped

  app2:
    image: ************:9092/ai_server:latest
    ports:
      - "30102:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped

  app3:
    image: ************:9092/ai_server:latest
    ports:
      - "30103:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped
```
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app1
      - app2
      - app3
    restart: unless-stopped

  app1:
    image: ************:9092/ai_server:latest
    ports:
      - "30101:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped

  app2:
    image: ************:9092/ai_server:latest
    ports:
      - "30102:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped

  app3:
    image: ************:9092/ai_server:latest
    ports:
      - "30103:30101"
    environment:
      - ENVIRONMENT=production
      - DEBUG=false
    volumes:
      - /data/server/logs:/app/logs
    restart: unless-stopped
```

#### 3.2.4 滚动更新脚本
创建 `scripts/rolling_update.sh`：

```bash
#!/bin/bash

# 滚动更新脚本
SERVICES=("app1" "app2" "app3")

for service in "${SERVICES[@]}"; do
    echo "更新服务: $service"
    
    # 停止服务
    docker-compose stop $service
    
    # 更新服务
    docker-compose pull $service
    
    # 启动服务
    docker-compose up -d $service
    
    # 等待服务启动
    echo "等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    echo "检查服务健康状态..."
    # 这里可以添加健康检查逻辑
    
    echo "服务 $service 更新完成"
    echo "------------------------"
done

echo "所有服务更新完成"
```
#!/bin/bash

# 滚动更新脚本
SERVICES=("app1" "app2" "app3")

for service in "${SERVICES[@]}"; do
    echo "更新服务: $service"
    
    # 停止服务
    docker-compose stop $service
    
    # 更新服务
    docker-compose pull $service
    
    # 启动服务
    docker-compose up -d $service
    
    # 等待服务启动
    echo "等待服务启动..."
    sleep 10
    
    # 检查服务健康状态
    echo "检查服务健康状态..."
    # 这里可以添加健康检查逻辑
    
    echo "服务 $service 更新完成"
    echo "------------------------"
done

echo "所有服务更新完成"
```

## 4. 最佳实践和注意事项

### 4.1 应用层优化
1. **实现优雅关闭**：在应用中处理 SIGTERM 信号，确保正在处理的请求完成：
   ```python
   import signal
   import asyncio
   import logging
   
   # 全局变量用于跟踪活跃连接
   active_connections = 0
   
   def handle_shutdown(signum, frame):
       logging.info(f"收到关闭信号 {signum}，正在优雅关闭...")
       # 在实际应用中，你可能需要：
       # 1. 停止接收新请求
       # 2. 等待当前请求处理完成
       # 3. 关闭数据库连接等资源
       # 4. 执行其他清理操作
       
       # 示例：等待所有活跃连接完成（简化版）
       # 在实际应用中，你可能需要更复杂的逻辑
       print("正在等待当前请求处理完成...")
       # 这里可以添加等待逻辑
   
   # 注册信号处理器
   signal.signal(signal.SIGTERM, handle_shutdown)
   signal.signal(signal.SIGINT, handle_shutdown)
   ```

2. **健康检查端点**：确保 `/health` 端点能准确反映应用状态：
   ```python
   @app.get("/health", response_model=HealthCheckResponse, tags=["系统"])
   async def health_check():
       # 检查数据库连接等关键组件
       db_healthy = check_database_connection()
       redis_healthy = check_redis_connection()
       
       if db_healthy and redis_healthy:
           return HealthCheckResponse(
               status="healthy",
               timestamp=time.time(),
               version=settings.VERSION,
               environment=settings.ENVIRONMENT,
           )
       else:
           # 返回503状态码表示服务不健康
           raise HTTPException(status_code=503, detail="Service Unhealthy")
   ```

3. **连接管理**：正确管理数据库连接和其他外部资源连接：
   ```python
   # 使用连接池管理数据库连接
   from sqlalchemy import create_engine
   from sqlalchemy.pool import QueuePool
   
   # 创建带连接池的引擎
   engine = create_engine(
       DATABASE_URL,
       poolclass=QueuePool,
       pool_size=10,
       max_overflow=20,
       pool_pre_ping=True,  # 检查连接有效性
       pool_recycle=3600,   # 1小时后回收连接
   )
   ```

### 4.2 部署层优化
1. **使用进程管理器**：考虑使用 Supervisor 或 systemd 管理应用进程：
   ```ini
   # supervisord 配置示例
   [program:ai_server]
   command=poetry run uvicorn app.main:app --host 0.0.0.0 --port 30101 --workers 4
   directory=/path/to/your/app
   user=www-data
   autostart=true
   autorestart=true
   redirect_stderr=true
   stdout_logfile=/var/log/ai_server.log
   stopsignal=TERM
   stopwaitsecs=60
   ```

2. **资源限制**：为容器设置适当的内存和CPU限制：
   ```yaml
   # docker-compose 资源限制示例
   services:
     app:
       image: ************:9092/ai_server:latest
       deploy:
         resources:
           limits:
             memory: 1G
             cpus: '0.5'
           reservations:
             memory: 512M
             cpus: '0.25'
   ```

3. **日志管理**：统一日志收集和管理：
   ```yaml
   # Docker 日志配置
   services:
     app:
       image: ************:9092/ai_server:latest
       logging:
         driver: "json-file"
         options:
           max-size: "10m"
           max-file: "3"
   ```

### 4.2 部署层优化
1. **使用进程管理器**：考虑使用 Supervisor 或 systemd 管理应用进程

2. **资源限制**：为容器设置适当的内存和CPU限制

3. **日志管理**：统一日志收集和管理

### 4.3 监控和告警
1. **部署监控**：监控部署过程中的状态变化

2. **性能监控**：监控应用性能指标

3. **错误告警**：设置适当的错误告警机制

## 5. 实施建议

### 5.1 短期实施（1-2周）
1. 实现方案 3.1，优化现有部署流程
2. 添加健康检查和基本监控
3. 测试平滑重启功能

### 5.2 中期实施（1-2个月）
1. 部署反向代理和多实例架构
2. 实现滚动更新机制
3. 完善监控和告警系统

### 5.3 长期规划
1. 考虑使用 Kubernetes 实现更高级的部署策略
2. 实现自动扩缩容
3. 完善服务网格架构

## 6. 总结

实现 FastAPI 应用的平滑重启需要从多个层面考虑：
1. 应用层：正确处理信号和连接
2. 部署层：使用合适的部署策略
3. 基础设施层：引入反向代理和负载均衡

建议从短期方案开始实施，逐步过渡到更完善的架构。

## 7. 实施检查清单

### 短期实施检查清单
- [x] 更新 Dockerfile 添加健康检查和多worker支持
- [x] 创建平滑重启脚本
- [x] 更新应用代码实现优雅关闭
- [x] 更新 Makefile 使用多worker模式
- [x] 创建测试脚本验证功能
- [x] 更新文档说明

### 中期实施检查清单
- [ ] 更新 CI/CD 流程集成平滑重启
- [ ] 部署 Nginx 反向代理
- [ ] 配置多实例应用
- [ ] 实现滚动更新机制
- [ ] 完善健康检查机制
- [ ] 建立完整的监控体系

### 长期规划
- [ ] 考虑迁移到 Kubernetes
- [ ] 实现自动扩缩容
- [ ] 建立服务网格架构
- [ ] 完善灾难恢复机制