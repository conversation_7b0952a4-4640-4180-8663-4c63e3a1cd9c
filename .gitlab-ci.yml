stages:
  - build
  - deploy

build:
  stage: build
  tags:
    - shell
  script:
    - docker rmi ************:9092/ai_server:latest || true
    - docker build -t ************:9092/ai_server:latest .
    - docker push ************:9092/ai_server:latest
  only:
    - main

deploy:
  stage: deploy
  tags:
    - shell
  before_script:
    - eval "$(ssh-agent -s)"
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $SSH_HOST >> ~/.ssh/known_hosts
  script:
    - ssh -v $SSH_USER@$SSH_HOST
    - ssh $SSH_USER@$SSH_HOST "docker pull ************:9092/ai_server:latest"
    # 上传平滑重启脚本
    - scp scripts/graceful_restart.sh $SSH_USER@$SSH_HOST:/tmp/
    - ssh $SSH_USER@$SSH_HOST "chmod +x /tmp/graceful_restart.sh"
    # 执行平滑重启
    - ssh $SSH_USER@$SSH_HOST "DIFY_SOP_TYPE1_API_KEY='$DIFY_SOP_TYPE1_API_KEY' DIFY_SOP_TYPE2_API_KEY='$DIFY_SOP_TYPE2_API_KEY' DIFY_API_URL='$DIFY_API_URL' DIFY_USER='$DIFY_USER' /tmp/graceful_restart.sh"
  environment:
    name: production
  only:
    - main